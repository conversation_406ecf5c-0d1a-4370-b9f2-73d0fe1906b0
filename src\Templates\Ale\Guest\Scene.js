import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions'
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import LoaderOverlay from "../../../components/LoaderOverlay";

class AleGuest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ale_link: "",
      loading: true,
      error: null,
    }
  }
  componentDidMount() {
    this.props.SubscribeToCustom((msg) => {
      var data = JSON.parse(msg);
      var contentWindow = document.getElementById('showcase_frame').contentWindow
      contentWindow.postMessage({ ...data, simulate: true }, "*")
    })
    Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({ includeMetadataChanges: true }, (doc) => {
      const config_data = doc.data();
      // const tempLink = config_data.ale_link.replace('https://propvr-ui-library-k5aul2eu6q-uc.a.run.app', 'http://localhost:5173'); -------------->for testing in local
      if (config_data && config_data.ale_link) {
        let ale_link_with_guest_param;
        if (config_data.ale_link.includes("?")) {
          ale_link_with_guest_param = config_data.ale_link + "&salestoolGuest=true";
        } else {
          ale_link_with_guest_param = config_data.ale_link + "?salestoolGuest=true";
        }
        this.setState({
          ale_link: ale_link_with_guest_param,
          loading: false, // Stop loader once the link is set
        });
      } else {
        this.setState({
          error: "Configuration data is missing.",
          loading: false, // Stop loader and show error
        });
      }
    },
      (error) => {
        this.setState({
          error: "Failed to fetch configuration data: " + error.message,
          loading: false, // Stop loader and show error
        });
      }
    )
  }


  render() {
    const { ale_link, loading, error } = this.state;

    if (loading) {
      return (
        <LoaderOverlay
          title="Preparing Your Virtual Experience"
          message="Just a moment while we set the stage for your immersive journey..."
        />
      );
    }

    if (error) {
      return (
        <LoaderOverlay
          title="Error Loading Scene"
          message={error}
        />
      );
    }

    return (
      <iframe
        id="showcase_frame"
        title="Damac London Scene"
        src={ale_link}
        style={{ width: "100%", height: "100%", position: "absolute" }}
      />
    );
  }
}
const mapStateToProps = state => {
  return {
    configDetails: state.Sessions.configData,
  }
}
const mapDispatchTothisprops = {
  ...Sessions
}
export default connect(mapStateToProps, mapDispatchTothisprops)(AleGuest);