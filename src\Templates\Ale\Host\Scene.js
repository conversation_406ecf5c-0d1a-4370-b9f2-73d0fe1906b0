import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions';
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import * as HostActions from '../../../Actions/HostAction'
import { getCookie } from '../../../Tools/helpers/domhelper'
import LoaderOverlay from "../../../components/LoaderOverlay";

class Ale extends Component {
    constructor(props) {
        super(props);
        this.UpdateConfig = this.UpdateConfig.bind(this);
        this.state = {
            updatedLink:''
        }
    }
    componentDidMount() {

      Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").get()
      .then(doc => {
        if (!doc.data().ale_link) {
          if (this.props.ProjectDetails.projectSettings && this.props.ProjectDetails.projectSettings.ale && this.props.ProjectDetails.projectSettings.ale.initial_scene_id) {
            const ale_link =
            process.env.REACT_APP_API_PROPVR_UI_LIBRARY +
            `${getCookie("organization")}/${
              this.props.ProjectDetails.projectSettings.ale.initial_scene_type === "project"
                ? "projectscene"
                : "masterscene"
            }/${this.props.ProjectDetails._id}/${this.props.ProjectDetails.projectSettings.ale.initial_scene_id}`;
                      this.setState({
              updatedLink: ale_link
            })
            return Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").set({
              ale_link: ale_link,
            });
          }
        } else {

          console.log("doc.data",doc.data());
          this.setState({
            updatedLink: doc.data().ale_link
          })
          this.props.SetConfigData(doc.data());
        }
      })
      .then(data => {
        if (data) {
          this.props.SetConfigData(data.data());
        }
      })
      .catch(error => {
        
      });
    


          window.addEventListener('message', ({data}) => {
            const Data = typeof data == 'string'?JSON.parse(data):data;
            if(Data.actiontype=="routechange"){
              this.UpdateConfig(Data.url)
            }else{
               this.props.SendCustomMessage( Data,this.props.roomId)
            }
          });
  }
  UpdateConfig(url){
    Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").update({
        ale_link: url,
    }).catch(error=>{
        
    })
    }
    render() {
      const { updatedLink } = this.state;
  
      return updatedLink ? (
        <iframe style={{ width: "100%", height: "100%", position: "absolute" }} src={updatedLink} />
      ) : (
        <LoaderOverlay
          title="Preparing Your Virtual Experience"
          message="Just a moment while we set the stage for your immersive journey..."
        />
      );
    }
}

const mapStateToProps = state => {
    return {
      Config: state.Call.config,
      configDetails: state.Sessions.configData,
      ProjectDetails: state.Sessions.projectDetails
    }
  }
const mapDispatchToProps = {
    ...Sessions,
    ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(Ale);
