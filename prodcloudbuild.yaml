steps:

# Build the container image
- name: gcr.io/cloud-builders/docker
  args: ['build', '-t', 'gcr.io/$PROJECT_ID/github.com/propvrtwt/organization_salestool/$BRANCH_NAME:${COMMIT_SHA}','-f','proddockerfile', '.']
  id: Building the container image


# Push the container image to Container Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/$PROJECT_ID/github.com/propvrtwt/organization_salestool/$BRANCH_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry

  


# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'gcr.io/$PROJECT_ID/github.com/propvrtwt/organization_salestool/$BRANCH_NAME:${COMMIT_SHA}', '--region', 'us-central1', '--platform', 'managed', "--allow-unauthenticated"]