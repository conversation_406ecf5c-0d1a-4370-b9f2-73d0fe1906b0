import React, { useState, useEffect, useRef } from 'react';
import { PostRequestWithHeaders } from '../../Tools/helpers/api';

const ProjectSelectionModal = ({ isOpen, onClose, onSelectProject }) => {
  const [projects, setProjects] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState(null);
  const [switchingProject, setSwitchingProject] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const modalRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      // Reset selection when modal opens
      setSelectedProject(null);
      // Fetch projects from API
      fetchProjects();
      // Start animation
      setIsAnimating(true);
      // Add animation class after a small delay
      setTimeout(() => {
        if (modalRef.current) {
          modalRef.current.classList.add('translate-y-0');
        }
      }, 10);
    } else {
      setIsAnimating(false);
      if (modalRef.current) {
        modalRef.current.classList.remove('translate-y-0');
      }
    }
  }, [isOpen]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      console.log("Fetching projects from API...");
      console.log("API URL:", `${process.env.REACT_APP_API_BACKEND_API_URL}org/ListProjectsFromOrganization`);

      // Using the PostRequestWithHeaders helper
      const response = await PostRequestWithHeaders({
        url: `${process.env.REACT_APP_API_BACKEND_API_URL}org/ListProjectsFromOrganization`,
      });

      console.log("API response received:", response);
      console.log("Response type:", typeof response);

      // Check if the response has the expected format
      if (response) {
        let projectsArray = [];

        console.log("Response structure:", Object.keys(response));

        // Handle the specific response structure with projectsObj
        if (response.projectsObj) {
          console.log("Found projectsObj with keys:", Object.keys(response.projectsObj));
          projectsArray = Object.values(response.projectsObj);
        }
        // Handle case where response is the data property containing projectsObj
        else if (response.data && response.data.projectsObj) {
          console.log("Found data.projectsObj with keys:", Object.keys(response.data.projectsObj));
          projectsArray = Object.values(response.data.projectsObj);
        }
        // Handle case where response is already an array
        else if (Array.isArray(response)) {
          console.log("Response is an array with length:", response.length);
          projectsArray = response;
        }
        // Handle case where response is a simple object of projects
        else if (typeof response === 'object') {
          console.log("Response is an object with keys:", Object.keys(response));
          projectsArray = Object.values(response);
        }

        // Filter out any null or undefined values
        const validProjects = projectsArray.filter(project => project != null);
        console.log("Valid projects count:", validProjects.length);

        // Log the first project to see its structure
        if (validProjects.length > 0) {
          console.log("Sample project structure:", validProjects[0]);
        }

        setProjects(validProjects);
      } else {
        console.warn("Unexpected API response structure:", response);
        setProjects([]);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching projects:", error);
      setLoading(false);
    }
  };

  // Removed the switchProject function that was calling the switch API

const experienceCards = projects.flatMap(project => {
  const matchesSearch = project?.name?.toLowerCase().includes(searchQuery.toLowerCase());
  if (!matchesSearch) return [];

  const cards = [];

  if (project.projectSettings?.pixelstreaming?.is_enabled) {
    cards.push({
      ...project,
      experienceType: 'pixelstreaming'
    });
  }

  if (project.projectSettings?.ale?.is_enabled) {
    cards.push({
      ...project,
      experienceType: 'ale'
    });
  }

  return cards;
});


  const handleProjectSelect = (project) => {
    console.log("Selected project:", project);
    setSelectedProject(project);
  };

  const handleShareButtonClick = () => {
    if (!selectedProject) return;

    setSwitchingProject(true);

    // Directly call the parent's onSelectProject without the switch API
    setTimeout(() => {
      onSelectProject(selectedProject);
      setSwitchingProject(false);
      handleClose();
    }, 500); // Small delay for UI feedback
  };

  // Custom close handler with animation
  const handleClose = () => {
    // For mobile, animate out first
    if (window.innerWidth < 640) { // sm breakpoint
      if (modalRef.current) {
        modalRef.current.classList.remove('translate-y-0');
        // Wait for animation to complete before closing
        setTimeout(() => {
          onClose();
        }, 300);
      } else {
        onClose();
      }
    } else {
      // For desktop, close immediately
      onClose();
    }
  };
  
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-end sm:items-center justify-center bg-black bg-opacity-50"
      onClick={(e) => {
        // Close when clicking the backdrop (not the modal itself)
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}>
      <div
        ref={modalRef}
        className="bg-white w-full sm:rounded-lg rounded-t-xl sm:max-w-4xl p-2 max-h-[90vh] sm:max-h-[90vh] flex flex-col transition-all duration-300 ease-in-out transform sm:translate-y-0 translate-y-full">
        {/* Drag indicator for mobile */}
        <div className="sm:hidden flex justify-center mb-1">
          <div className="w-10 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Header with close button */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Select Project</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 1L13 13M1 13L13 1" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </div>

        {/* Search */}
        <div className="mb-4">
          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.3453 11.7425C9.02362 12.7107 7.38492 13.1444 5.75708 12.9567C4.12923 12.769 2.6323 11.9739 1.56576 10.7303C0.499223 9.48666 -0.0582671 7.88635 0.00482557 6.2495C0.0679182 4.61265 0.74694 3.05997 1.90604 1.90209C3.06515 0.744221 4.61885 0.0665459 6.25631 0.00464636C7.89378 -0.0572531 9.49424 0.501188 10.7375 1.56825C11.9808 2.63531 12.7752 4.13229 12.9617 5.7597C13.1483 7.38711 12.7133 9.02494 11.7438 10.3455V10.3445C11.7838 10.3745 11.8218 10.4065 11.8588 10.4425L15.71 14.2924C15.8977 14.4799 16.0032 14.7342 16.0033 14.9995C16.0034 15.2647 15.8981 15.5192 15.7105 15.7068C15.5229 15.8944 15.2685 15.9999 15.0031 16C14.7378 16.0001 14.4833 15.8948 14.2956 15.7073L10.4444 11.8575C10.4082 11.8217 10.3747 11.7833 10.3443 11.7425H10.3453ZM6.50016 12.0004C7.22265 12.0004 7.93807 11.8582 8.60557 11.5818C9.27306 11.3054 9.87957 10.9003 10.3904 10.3896C10.9013 9.87889 11.3066 9.2726 11.5831 8.60533C11.8596 7.93807 12.0019 7.2229 12.0019 6.50065C12.0019 5.77841 11.8596 5.06324 11.5831 4.39597C11.3066 3.72871 10.9013 3.12242 10.3904 2.61171C9.87957 2.10101 9.27306 1.6959 8.60557 1.41951C7.93807 1.14312 7.22265 1.00086 6.50016 1.00086C5.04101 1.00086 3.64164 1.5803 2.60987 2.61171C1.5781 3.64312 0.998456 5.04202 0.998456 6.50065C0.998456 7.95929 1.5781 9.35818 2.60987 10.3896C3.64164 11.421 5.04101 12.0004 6.50016 12.0004Z" fill="#5B616E"/>
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search project"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-gray-100 rounded-[18px] placeholder:text-left placeholder:text-sm text-sm outline-none"
            />
          </div>
        </div>

        {/* No Results Message */}
        {!loading && experienceCards.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No projects found matching "{searchQuery}"
          </div>
        )}

        {/* Projects Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-3 overflow-y-auto h-full max-h-[50vh] sm:max-h-[60vh] px-1 py-1">
          {experienceCards.map((project, index) => (
            <div
  key={`${project._id}-${project.experienceType}-${index}`}
  onClick={() => handleProjectSelect(project)}
  className={`cursor-pointer hover:shadow-lg border-4 ${selectedProject?._id === project._id && selectedProject.experienceType === project.experienceType ? 'border-blue-500' : 'border-transparent'} hover:border-blue-500 rounded-lg overflow-hidden h-28 sm:h-32 relative group`}
>
  <div className="relative w-full h-full">
    <img
      src={project.project_thumbnail || 'https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fsiteassets%2Fplaceholder.jpg?alt=media'}
      alt={project.name}
      className="object-cover w-full h-full rounded"
    />
    <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center transition-all duration-300">
      <p className="text-white text-md font-semibold text-center px-2 leading-tight">
        {project.name}
      </p>
      <p className="text-white text-xs font-medium mt-1">
        {project.experienceType === 'pixelstreaming' ? 'Metaverse' : 'ALE'}
      </p>
    </div>
  </div>
</div>

          ))}
        </div>

        {/* Share Button */}
        <div className="mt-3 flex justify-center">
          <button
            onClick={handleShareButtonClick}
            disabled={!selectedProject || loading || switchingProject}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-1.5 px-6 rounded-lg"
          >
            {switchingProject ? "Switching..." : "Share"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProjectSelectionModal;