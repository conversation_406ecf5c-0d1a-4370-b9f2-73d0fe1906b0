// TabletControls.jsx
import React, { useState, useEffect, useRef } from 'react';
import {
    MicIcon, MicOffIcon, VideoIcon, VideoOffIcon,
    UsersIcon, ChatIcon, EndCallIcon, EyeIcon, EyeOffIcon,
    StopScreenShareIcon, ActiveChat, ActiveMember, ActiveDropdown
} from '../../assets/SvgIcons';
import Timer from '../../Tools/ToolComponents/Timer';
import GuestTimer from '../../Tools/ToolComponents/GuestTimer';


const TabletControls = ({
    Audio, Video, Tab, ChatOpen, MembersOpen, UnreadCount, userscount,
    handleAudioControl, handleVideoControl,
    handleTabControl, handleEndSession, handleStopSharing,
    toggleFullscreen, handleInvertControls,
    ShowControls, handleShareProject, switchingProject,
    SessionDetails, roomId,
    isGuest = false
}) => {
    const [showMenu, setShowMenu] = useState(false);
    const menuRef = useRef(null);

    const toggleMenu = () => {
        setShowMenu(!showMenu);
    };

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setShowMenu(false);
            }
        };

        // Add event listener when menu is open
        if (showMenu) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showMenu]);

    return (
        <div className="w-full z-40 bg-white py-2 px-2">
            <div className="flex items-center justify-between">
                {/* Left side - Timer */}
                <div className="flex items-center">

                    {/* Timer component */}
                    {isGuest ? (
                        <GuestTimer
                            position={"top"}
                            Config={SessionDetails}
                            start={SessionDetails?.start_time || new Date()}
                            roomId={roomId}
                            trackInactivity={false}
                            trackMaxtimeout={false}
                        />
                    ) : (
                        <Timer
                            position={"top"}
                            start={SessionDetails?.start || new Date()}
                            roomId={roomId}
                            trackInactivity={false}
                            trackMaxtimeout={true}
                            SessionDetails={SessionDetails}
                        />
                    )}
                </div>

                {/* Right side  */}
                <div className="flex items-center justify-evenly gap-2.5">

                    <button
                        onClick={handleVideoControl}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center
                        ${Video ? 'bg-[#F3F4F6]' : 'bg-[#FDF2F2]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            {Video ? <VideoIcon /> : <VideoOffIcon />}
                        </div>
                    </button>

                    <button
                        onClick={handleAudioControl}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center
              ${Audio ? 'bg-[#F3F4F6]' : 'bg-[#FDF2F2]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            {Audio ? <MicIcon /> : <MicOffIcon />}
                        </div>
                    </button>

                    <button
                        onClick={() => handleTabControl("CHAT")}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center relative
              ${ChatOpen ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] border-[none]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            {ChatOpen ? <ActiveChat /> : <ChatIcon />}
                        </div>
                        {UnreadCount > 0 && (
                            <span className="absolute top-0 right-0 bg-[#ff4444] text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                {UnreadCount}
                            </span>
                        )}
                    </button>

                    <button
                        onClick={() => handleTabControl("MEMBERS")}
                        className={`w-8 h-8 rounded-lg flex items-center justify-center relative
                        ${MembersOpen ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] border-[none]'}`}
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            {MembersOpen ? <ActiveMember /> : <UsersIcon />}
                        </div>
                        {userscount > 0 && (
                            <span className="absolute top-0 right-0 bg-[#ff4444] text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                {userscount}
                            </span>
                        )}
                    </button>

                    <button
                        onClick={() => toggleFullscreen(true)}
                        className="w-8 h-8 rounded-lg bg-[#F3F4F6] hover:bg-[#E5E7EB] flex items-center justify-center"
                    >
                        <div className="flex items-center justify-center w-5 h-5">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011 1z" clipRule="evenodd" />
                            </svg>
                        </div>
                    </button>

                    {/* Share Project and Stop Sharing buttons - only for hosts */}
                    {!isGuest && (
                        // Check if a project is being shared
                        SessionDetails?.project_id && SessionDetails.project_id !== 'undefined' ? (
                            <div className="flex items-center gap-2">
                                <button
                                    onClick={handleShareProject}
                                    className="flex items-center bg-[#F3F4F6] hover:bg-[#E5E7EB] rounded-lg px-3 py-1 h-8"
                                >
                                    <span className="text-gray-700 font-medium truncate max-w-[150px]">
                                        {SessionDetails?.projectName || "Project"}
                                    </span>
                                    <svg width="10" height="6" className="ml-2" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 4.99997C9 4.99997 6.05407 1 5 1C3.94587 1 1 5 1 5" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </button>
                                <button
                                    onClick={handleStopSharing}
                                    className="w-8 h-8 rounded-lg bg-[#F3F4F6] hover:bg-[#E5E7EB] flex items-center justify-center"
                                >
                                    <div className="flex items-center justify-center w-5 h-5">
                                        <StopScreenShareIcon />
                                    </div>
                                </button>
                            </div>
                        ) : (
                            <button
                                onClick={handleShareProject}
                                disabled={switchingProject}
                                className={`flex bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded-lg items-center justify-center w-40 z-10 ${switchingProject ? 'opacity-75' : ''}`}
                            >
                                {switchingProject ? (
                                    <>
                                        <span className="mr-2 animate-spin inline-block h-4 w-4 rounded-full border-1 border-white border-t-transparent"></span>
                                        Switching...
                                    </>
                                ) : (
                                    <>
                                        <svg width="16" height="16" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                                            <path d="M9.35883 0.603879C9.20615 0.670162 9.07018 0.763677 8.95586 0.881838L4.04027 5.96277C3.55965 6.45955 3.57268 7.2495 4.06943 7.73016C4.56618 8.21082 5.35607 8.19779 5.83669 7.701L8.65526 4.78763L8.83233 15.5195C8.84374 16.2107 9.41288 16.7602 10.1028 16.7488C10.7927 16.7374 11.3434 16.1695 11.332 15.4783L11.1549 4.74639L14.0681 7.56519C14.5648 8.04585 15.3547 8.03281 15.8353 7.53603C16.075 7.28827 16.1922 6.96627 16.1869 6.64629C16.1817 6.3263 16.0539 6.00834 15.8062 5.76863L10.7256 0.852637C10.6087 0.738292 10.4685 0.649334 10.3137 0.588124C10.0067 0.466914 9.66171 0.472606 9.35883 0.603879Z" fill="white" />
                                            <path d="M17.5399 12.8753L17.6018 16.6251C17.6131 17.315 17.0612 17.8843 16.3726 17.8956L3.87426 18.1019C3.1856 18.1132 2.61519 17.5625 2.6038 16.8725L2.54193 13.1228C2.53053 12.4316 1.96139 11.8821 1.27148 11.8935C0.581575 11.9048 0.0308697 12.4728 0.0422745 13.164L0.104145 16.9138C0.138256 18.9812 1.84829 20.6358 3.91551 20.6017L16.4138 20.3955C18.481 20.3614 20.1355 18.6512 20.1014 16.5838L20.0396 12.8341C20.0281 12.1428 19.459 11.5934 18.7691 11.6048C18.0792 11.6161 17.5285 12.1841 17.5399 12.8753Z" fill="white" />
                                        </svg>
                                        Share Project
                                    </>
                                )}
                            </button>
                        )
                    )}

                    {/* Stop Sharing button removed - now in dropdown menu */}

                    <div className="relative" ref={menuRef}>
                        <button
                            onClick={toggleMenu}
                            className={`w-8 h-8 rounded-lg flex items-center justify-center ${showMenu ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] hover:bg-[#E5E7EB] border-[none]'}`}
                        >
                            <div className="flex items-center justify-center w-5 h-5">
                                {showMenu ? <ActiveDropdown /> : (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                    </svg>
                                )}
                            </div>
                        </button>

                        {showMenu && (
                            <div className="absolute top-10 right-0 bg-white rounded-lg shadow-lg p-2 w-48 z-50">
                                <button
                                    onClick={() => {
                                        handleInvertControls();
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md"
                                >
                                    <EyeIcon />
                                    <span>Hide Controls</span>
                                </button>

                                {/* Stop Sharing option - only shown for hosts when a project is being shared */}
                                {!isGuest && SessionDetails?.project_id && SessionDetails.project_id !== 'undefined' && (
                                    <button
                                        onClick={() => {
                                            handleStopSharing();
                                            setShowMenu(false);
                                        }}
                                        className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md"
                                    >
                                        <div className="flex items-center justify-center w-5 h-5">
                                            <StopScreenShareIcon />
                                        </div>
                                        <span>Stop Sharing</span>
                                    </button>
                                )}

                                {/* End Session button - always shown */}
                                <button
                                    onClick={() => {
                                        handleEndSession();
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-red-500"
                                >
                                    <EndCallIcon />
                                    <span>End Session</span>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TabletControls;