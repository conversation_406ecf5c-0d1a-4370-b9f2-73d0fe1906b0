// src/components/CloudStreaming/CloudStreamingView.jsx
import React from "react";
import { LarkSR } from "larksr_websdk";
import PxyWebCommonUI from 'pxy_webcommonui';

const { Capabilities } = PxyWebCommonUI;

export default class CloudStreamingView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      remoteReady: false,
      isConnecting: true,
      connectionError: null,
      sdkConnected: false,
    }
    this.myRef = React.createRef();
    this.uiContainerRef = React.createRef();
    this.uiKeyboardRef = React.createRef();
  }

  componentDidMount() {
    this.initializeLarkSR();
  }

  componentDidUpdate(prevProps) {
    // Check if we need to reinitialize due to prop changes
    const authCodeChanged = prevProps.authCode !== this.props.authCode;
    const applicationIdChanged = prevProps.applicationId !== this.props.applicationId;

    // Only reinitialize if we have valid props and something meaningful changed
    if (this.props.authCode &&
        this.props.applicationId &&
        (authCodeChanged || applicationIdChanged)) {

      // Prevent reinitialization if already connecting or if we don't have a previous state to compare
      if (!this.state.isConnecting && (prevProps.authCode || prevProps.applicationId)) {
        console.log("Reinitializing LarkSR due to prop changes:", {
          authCodeChanged,
          applicationIdChanged,
          newAuthCode: this.props.authCode,
          newApplicationId: this.props.applicationId
        });

        this.reinitializeLarkSR();
      }
    }
  }

  // Separate method for reinitialization with proper cleanup sequencing
  async reinitializeLarkSR() {
    // Set connecting state immediately to prevent multiple reinitializations
    this.setState({
      isConnecting: true,
      connectionError: null,
      sdkConnected: false,
      remoteReady: false
    });

    try {
      // Step 1: Cleanup existing instance
      await this.cleanup();

      // Step 2: Wait a bit to ensure complete cleanup
      await new Promise(resolve => setTimeout(resolve, 100));

      // Step 3: Clear any remaining DOM elements in the container
      if (this.myRef.current) {
        // Remove all child elements except our UI containers
        const children = Array.from(this.myRef.current.children);
        children.forEach(child => {
          if (child !== this.uiContainerRef.current &&
              child !== this.uiKeyboardRef.current &&
              !child.classList.contains('loading-overlay') &&
              !child.classList.contains('error-overlay')) {
            child.remove();
          }
        });
      }

      // Step 4: Initialize new instance
      this.initializeLarkSR();

    } catch (error) {
      console.error("Error during LarkSR reinitialization:", error);
      this.setState({
        connectionError: `Reinitialization failed: ${error.message}`,
        isConnecting: false,
        sdkConnected: false
      });
    }
  }

  initializeLarkSR() {
    console.log("Initializing LarkSR...");

    // Validate DOM element is available
    if (!this.myRef.current) {
      const error = "Root element not available for LarkSR initialization";
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    // Validate required props
    const applicationId = this.props.applicationId;
    const authCode = this.props.authCode;

    if (!authCode || !applicationId) {
      const error = "Missing required props: authCode or applicationId";
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    // Validate auth code format (should be at least 8 characters)
    if (authCode.length < 8) {
      const error = `Invalid auth code format. Expected at least 8 characters, got ${authCode.length}`;
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    try {
      // Create new LarkSR instance
      const larksr = new LarkSR({
        rootElement: this.myRef.current,
        serverAddress: "https://ps.propvr.io:8181/",
        useSeparateMediaSharePeer: true,
      });

      const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";

      console.log("LarkSR instance created, initializing SDK...", {
        applicationId,
        authCodeLength: authCode.length,
        sdkId
      });

      // Initialize SDK and connect
      larksr.initSDKAuthCode(sdkId)
        .then(() => {
          console.log("Host: SDK auth code initialized successfully");
          return larksr.connect({
            appliId: applicationId,
            playerMode: 0,
            userType: 1,
            authCode: authCode
          });
        })
        .then(() => {
          console.log("Host: LarkSR connected successfully");
          this.setState({
            isConnecting: false,
            connectionError: null,
            sdkConnected: true
          });

          // Notify parent component of successful connection with SDK ID
          if (this.props.onConnectionSuccess) {
            this.props.onConnectionSuccess(sdkId);
          }
        })
        .catch((e) => {
          console.error("Host: LarkSR connection error:", e);
          console.error("Error details:", {
            message: e.message,
            code: e.code,
            authCode: authCode,
            applicationId: applicationId
          });

          const errorMessage = e.message || JSON.stringify(e);
          this.setState({
            connectionError: `Authentication failed: ${errorMessage}`,
            isConnecting: false,
            sdkConnected: false
          });

          // Notify parent component of connection error
          if (this.props.onConnectionError) {
            this.props.onConnectionError(e);
          }
        });

      this.setupEventListeners(larksr);
      this.larksr = larksr;

      if (Capabilities.isMobile) {
        this.initializeMobileControls();
      }

    } catch (error) {
      console.error("Error creating LarkSR instance:", error);
      this.setState({
        connectionError: `Failed to create LarkSR instance: ${error.message}`,
        isConnecting: false,
        sdkConnected: false
      });
    }
  }

  async cleanup() {
    console.log("Starting LarkSR cleanup...");

    try {
      // Cleanup mobile controls first
      if (this.joystick) {
        this.joystick.destroy();
        this.joystick = null;
      }
      if (this.keyboard) {
        this.keyboard.destroy();
        this.keyboard = null;
      }

      // Cleanup LarkSR instance
      if (this.larksr) {
        // Remove event listeners before destroying
        const events = [
          'connect', 'gotremotesteam', 'meidaloaded',
          'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
          'error', 'info', 'apprequestinput', 'resourcenotenough'
        ];

        events.forEach(event => {
          try {
            this.larksr.off(event);
          } catch (e) {
            console.warn(`Failed to remove ${event} listener:`, e);
          }
        });

        // Destroy the SDK instance
        try {
          this.larksr.destroy();
        } catch (e) {
          console.warn("Error destroying LarkSR instance:", e);
        }

        this.larksr = null;
      }

      // Clear any video/canvas elements that might be left behind
      if (this.myRef.current) {
        const videos = this.myRef.current.querySelectorAll('video');
        const canvases = this.myRef.current.querySelectorAll('canvas');

        videos.forEach(video => {
          try {
            video.pause();
            video.srcObject = null;
            video.src = '';
            video.load();
          } catch (e) {
            console.warn("Error cleaning up video element:", e);
          }
        });

        canvases.forEach(canvas => {
          try {
            const ctx = canvas.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          } catch (e) {
            console.warn("Error cleaning up canvas element:", e);
          }
        });
      }

      console.log("LarkSR cleanup completed successfully");

    } catch (error) {
      console.error("Error during LarkSR cleanup:", error);
      throw error;
    }
  }

  initializeMobileControls() {
    // Initialize mobile controls if needed
    // This method can be expanded later for mobile-specific controls
    console.log("Initializing mobile controls for LarkSR");
  }

  setupEventListeners(larksr) {
    const events = [
      'connect', 'gotremotesteam', 'meidaloaded', 
      'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
      'error', 'info', 'apprequestinput', 'resourcenotenough'
    ];

    events.forEach(event => {
      larksr.on(event, this.handleLarkEvent(event));
    });
  }

  handleLarkEvent = (eventName) => (e) => {
    console.log(`Host LarkSR ${eventName} Event:`, e);

    switch(eventName) {
      case 'connect':
        console.log("Host: LarkSR connected event received");
        this.setState({
          sdkConnected: true,
          isConnecting: false,
          connectionError: null
        });
        if (this.props.onConnectionSuccess) {
          // Pass the SDK ID to maintain consistency with the Promise chain
          const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";
          this.props.onConnectionSuccess(sdkId);
        }
        break;
      case 'meidaloaded':
        console.log("Host: Media loaded successfully");
        this.setState({ remoteReady: true });
        break;
      case 'mediaplaysuccess':
        console.log("Host: Media play success");
        this.joystick?.show();
        break;
      case 'error':
        console.error("Host: LarkSR error event:", e);
        const errorMessage = e.message || JSON.stringify(e);
        this.setState({
          connectionError: errorMessage,
          isConnecting: false,
          sdkConnected: false
        });
        if (this.props.onConnectionError) {
          this.props.onConnectionError(e);
        }
        break;
      case 'resourcenotenough':
        console.error("Host: Resource not enough");
        this.setState({
          connectionError: "Resource not enough - please try again later",
          isConnecting: false,
          sdkConnected: false
        });
        break;
    }
  }

  render() {
    return (
      <div ref={this.myRef} style={{ position: 'relative', width: '100%', height: '100%' }}>
        <div ref={this.uiContainerRef}
          style={{
            position: 'absolute',
            zIndex: 2000
          }}
        />
        <div ref={this.uiKeyboardRef}
          style={{
            position: 'absolute',
            zIndex: 2000,
            bottom: 0,
            width: '100%'
          }}
        />

        {/* Loading overlay */}
        {this.state.isConnecting && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-white text-xl font-semibold">Connecting to LarkXR...</p>
            <p className="text-gray-300 mt-2">Please wait while we establish the connection</p>
          </div>
        )}

        {/* Error overlay */}
        {this.state.connectionError && !this.state.isConnecting && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <p className="text-white text-xl font-semibold">Connection Error</p>
            <p className="text-gray-300 mt-2 text-center px-4">{this.state.connectionError}</p>
            <button
              onClick={() => {
                this.setState({ connectionError: null, isConnecting: true });
                this.initializeLarkSR();
              }}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry Connection
            </button>
          </div>
        )}
      </div>
    );
  }

  componentWillUnmount() {
    this.cleanup();
  }
}